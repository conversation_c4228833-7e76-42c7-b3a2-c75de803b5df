{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-82.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "_flutterfire_internals", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.55", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "analyzer", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-7.4.5", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "ansicolor", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ansicolor-2.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "archive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "audioplayers", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.4.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "audioplayers_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-5.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "audioplayers_darwin", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-6.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "audioplayers_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-4.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "audioplayers_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "audioplayers_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-5.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "audioplayers_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-4.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "auto_route", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_route-10.1.0+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "auto_route_generator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_route_generator-10.2.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "bloc", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "build", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_daemon", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_resolvers", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_runner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.15", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_runner_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "built_collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "carousel_slider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/carousel_slider-5.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "cart_stepper", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cart_stepper-4.3.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "checked_yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "cli_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_config-0.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "code_builder", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "convert", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "coverage", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/coverage-1.14.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "crypto", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "csslib", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dart_style", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-3.1.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "dbus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "device_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio_web_adapter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "dotted_border", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dotted_border-2.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dropdown_button2", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dropdown_button2-2.3.9", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "easy_localization", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/easy_localization-3.0.7+1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "easy_logger", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/easy_logger-0.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "equatable", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "excel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/excel-4.0.6", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "expandable", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/expandable-5.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "firebase_analytics", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_analytics_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.3.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_analytics_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+12", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_remote_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_remote_config_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config_platform_interface-1.5.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_remote_config_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config_web-1.8.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fixnum", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/Documents/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_bloc", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_chat_bubble", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_chat_bubble-2.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_inset_box_shadow", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inset_box_shadow-1.0.8", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_platform_interface-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_local_notifications", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_local_notifications_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_local_notifications_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_localizations", "rootUri": "file:///Users/<USER>/Documents/flutter/packages/flutter_localizations", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_mute", "rootUri": "../libraries/flutter_mute", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_pdfview", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.0+1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_rating_bar", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_rating_bar-4.0.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "flutter_sliding_up_panel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sliding_up_panel-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_svg", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_test", "rootUri": "file:///Users/<USER>/Documents/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_typeahead", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_typeahead-5.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_udid", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_udid-3.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_web_plugins", "rootUri": "file:///Users/<USER>/Documents/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_widget_from_html_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_widget_from_html_core-0.15.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "flutter_xlider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_xlider-3.5.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "frontend_server_client", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "get_it", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/get_it-8.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "glob", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "go_router", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_fonts", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "graphs", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "hive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "hive_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hotreloader-4.3.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_certificate_pinning", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_certificate_pinning-3.0.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "http_multi_server", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_parser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.3.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "intl", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "io", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "jiffy", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/jiffy-6.4.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "js", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.7.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "json_annotation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "jwt_decoder", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decoder-2.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lean_builder", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lean_builder-0.1.0-alpha.10", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "local_auth", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "local_auth_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "local_auth_darwin", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "local_auth_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "local_auth_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "logging", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "lottie", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "nested", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "node_preamble", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/node_preamble-2.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "open_filex", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_filex-4.7.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "overlay_support", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/overlay_support-2.1.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "package_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "package_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "package_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_drawing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_drawing-1.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path_parsing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "permission_handler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_apple", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "pinput", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pinput-5.0.1", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pointer_interceptor", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointer_interceptor-0.10.1+2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pointer_interceptor_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointer_interceptor_ios-0.10.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pointer_interceptor_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointer_interceptor_platform_interface-0.10.0+1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "pointer_interceptor_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointer_interceptor_web-0.10.2+1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "pool", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "pub_semver", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pubspec_parse", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "quiver", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "real_volume", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/real_volume-1.0.9", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "root_jailbreak_sniffer", "rootUri": "../libraries/root_jailbreak_sniffer-1.0.6", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "screenshot", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/screenshot-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "scrollable_positioned_list", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.3.8", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "shared_preferences", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shelf_packages_handler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_static", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_static-1.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf_web_socket", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shimmer", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/Documents/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sliding_up_panel2", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sliding_up_panel2-3.3.0+1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "smooth_page_indicator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sound_mode", "rootUri": "../libraries/sound_mode", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "source_gen", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-2.0.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "source_map_stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_maps", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_maps-0.10.13", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "stream_transform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "syncfusion_flutter_charts", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-29.2.7", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "syncfusion_flutter_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-29.2.7", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "syncfusion_flutter_datagrid", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_datagrid-29.2.7+1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "syncfusion_flutter_pdf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_pdf-29.2.7+1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "syncfusion_flutter_sliders", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_sliders-29.2.7", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "syncfusion_flutter_treemap", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_treemap-29.2.7", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "syncfusion_flutter_xlsio", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_xlsio-29.2.7", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "syncfusion_officecore", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_officecore-29.2.7", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "synchronized", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test-1.25.15", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "test_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_core-0.6.8", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "timeline_tile", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeline_tile-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "timezone", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "timing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "typed_data", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "universal_platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "url_launcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "url_launcher_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "uuid", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_graphics", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_codec", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_compiler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "very_good_analysis", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/very_good_analysis-6.0.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "watcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "webkit_inspection_protocol", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "webview_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "webview_flutter_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "webview_flutter_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "webview_flutter_wkwebview", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "win32", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "win32_registry", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "xdg_directories", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "xxh3", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xxh3-1.2.0", "packageUri": "lib/", "languageVersion": "2.16"}, {"name": "yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "scad_mobile", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.1"}], "generator": "pub", "generatorVersion": "3.8.0", "flutterRoot": "file:///Users/<USER>/Documents/flutter", "flutterVersion": "3.32.0", "pubCache": "file:///Users/<USER>/.pub-cache"}