{"roots": ["scad_mobile"], "packages": [{"name": "scad_mobile", "version": "1.0.10+26", "dependencies": ["audioplayers", "auto_route", "carousel_slider", "cart_stepper", "device_info_plus", "dio", "dotted_border", "dropdown_button2", "easy_localization", "equatable", "excel", "expandable", "firebase_analytics", "firebase_core", "firebase_remote_config", "flutter", "flutter_bloc", "flutter_chat_bubble", "flutter_inset_box_shadow", "flutter_local_notifications", "flutter_mute", "flutter_pdfview", "flutter_rating_bar", "flutter_sliding_up_panel", "flutter_svg", "flutter_typeahead", "flutter_udid", "flutter_widget_from_html_core", "flutter_xlider", "get_it", "go_router", "google_fonts", "hive_flutter", "http_certificate_pinning", "http_parser", "image", "intl", "jwt_decoder", "local_auth", "lottie", "open_filex", "overlay_support", "package_info_plus", "path_provider", "permission_handler", "pinput", "real_volume", "root_jailbreak_sniffer", "screenshot", "scrollable_positioned_list", "shimmer", "sliding_up_panel2", "smooth_page_indicator", "sound_mode", "syncfusion_flutter_charts", "syncfusion_flutter_core", "syncfusion_flutter_datagrid", "syncfusion_flutter_pdf", "syncfusion_flutter_sliders", "syncfusion_flutter_treemap", "syncfusion_flutter_xlsio", "timeline_tile", "url_launcher", "webview_flutter"], "devDependencies": ["auto_route_generator", "build_runner", "flutter_lints", "flutter_test", "very_good_analysis"]}, {"name": "very_good_analysis", "version": "6.0.0", "dependencies": []}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "build_runner", "version": "2.4.15", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web", "web_socket_channel", "yaml"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "auto_route_generator", "version": "10.2.3", "dependencies": ["analyzer", "auto_route", "build", "build_runner", "code_builder", "dart_style", "glob", "lean_builder", "path", "source_gen"]}, {"name": "auto_route", "version": "10.1.0+1", "dependencies": ["collection", "flutter", "meta", "path", "web"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "firebase_remote_config", "version": "5.4.4", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_remote_config_platform_interface", "firebase_remote_config_web", "flutter"]}, {"name": "excel", "version": "4.0.6", "dependencies": ["archive", "collection", "equatable", "xml"]}, {"name": "audioplayers", "version": "6.4.0", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_platform_interface", "audioplayers_web", "audioplayers_windows", "file", "flutter", "http", "meta", "path_provider", "synchronized", "uuid"]}, {"name": "real_volume", "version": "1.0.9", "dependencies": ["flutter"]}, {"name": "http_certificate_pinning", "version": "3.0.1", "dependencies": ["dio", "flutter", "http"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "flutter_udid", "version": "3.0.1", "dependencies": ["crypto", "flutter"]}, {"name": "open_filex", "version": "4.7.0", "dependencies": ["ffi", "flutter"]}, {"name": "flutter_sliding_up_panel", "version": "2.1.1", "dependencies": ["flutter"]}, {"name": "go_router", "version": "14.8.1", "dependencies": ["collection", "flutter", "flutter_web_plugins", "logging", "meta"]}, {"name": "flutter_typeahead", "version": "5.2.0", "dependencies": ["flutter", "flutter_keyboard_visibility", "pointer_interceptor"]}, {"name": "device_info_plus", "version": "10.1.2", "dependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "web", "win32", "win32_registry"]}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "lottie", "version": "3.3.0", "dependencies": ["archive", "flutter", "http", "path", "vector_math"]}, {"name": "webview_flutter", "version": "4.13.0", "dependencies": ["flutter", "webview_flutter_android", "webview_flutter_platform_interface", "webview_flutter_wkwebview"]}, {"name": "cart_stepper", "version": "4.3.0", "dependencies": ["flutter", "intl"]}, {"name": "package_info_plus", "version": "8.3.0", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "url_launcher", "version": "6.3.1", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "screenshot", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "image", "version": "4.3.0", "dependencies": ["archive", "meta", "xml"]}, {"name": "flutter_pdfview", "version": "1.4.0+1", "dependencies": ["flutter"]}, {"name": "syncfusion_flutter_datagrid", "version": "29.2.7+1", "dependencies": ["collection", "flutter", "syncfusion_flutter_core"]}, {"name": "syncfusion_flutter_treemap", "version": "29.2.7", "dependencies": ["flutter", "syncfusion_flutter_core"]}, {"name": "syncfusion_flutter_core", "version": "29.2.7", "dependencies": ["flutter", "vector_math"]}, {"name": "syncfusion_flutter_pdf", "version": "29.2.7+1", "dependencies": ["convert", "crypto", "flutter", "http", "intl", "syncfusion_flutter_core", "xml"]}, {"name": "syncfusion_flutter_charts", "version": "29.2.7", "dependencies": ["flutter", "intl", "syncfusion_flutter_core", "vector_math"]}, {"name": "syncfusion_flutter_xlsio", "version": "29.2.7", "dependencies": ["archive", "crypto", "flutter", "image", "intl", "jiffy", "syncfusion_officecore", "xml"]}, {"name": "syncfusion_flutter_sliders", "version": "29.2.7", "dependencies": ["flutter", "intl", "syncfusion_flutter_core"]}, {"name": "scrollable_positioned_list", "version": "0.3.8", "dependencies": ["collection", "flutter"]}, {"name": "flutter_inset_box_shadow", "version": "1.0.8", "dependencies": ["flutter"]}, {"name": "smooth_page_indicator", "version": "1.2.1", "dependencies": ["flutter"]}, {"name": "carousel_slider", "version": "5.0.0", "dependencies": ["flutter"]}, {"name": "overlay_support", "version": "2.1.0", "dependencies": ["async", "flutter"]}, {"name": "flutter_chat_bubble", "version": "2.0.2", "dependencies": ["flutter"]}, {"name": "timeline_tile", "version": "2.0.0", "dependencies": ["flutter"]}, {"name": "sliding_up_panel2", "version": "3.3.0+1", "dependencies": ["flutter"]}, {"name": "dropdown_button2", "version": "2.3.9", "dependencies": ["flutter", "meta"]}, {"name": "expandable", "version": "5.0.1", "dependencies": ["flutter"]}, {"name": "dotted_border", "version": "2.1.0", "dependencies": ["flutter", "path_drawing"]}, {"name": "pinput", "version": "5.0.1", "dependencies": ["flutter", "universal_platform"]}, {"name": "flutter_widget_from_html_core", "version": "0.15.2", "dependencies": ["csslib", "flutter", "html", "logging"]}, {"name": "flutter_rating_bar", "version": "4.0.1", "dependencies": ["flutter"]}, {"name": "shimmer", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "flutter_local_notifications", "version": "17.2.4", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "timezone"]}, {"name": "firebase_analytics", "version": "11.4.6", "dependencies": ["firebase_analytics_platform_interface", "firebase_analytics_web", "firebase_core", "firebase_core_platform_interface", "flutter"]}, {"name": "firebase_core", "version": "3.13.1", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "easy_localization", "version": "3.0.7+1", "dependencies": ["args", "easy_logger", "flutter", "flutter_localizations", "intl", "path", "shared_preferences"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "flutter_xlider", "version": "3.5.0", "dependencies": ["flutter"]}, {"name": "hive_flutter", "version": "1.1.0", "dependencies": ["flutter", "hive", "path", "path_provider"]}, {"name": "google_fonts", "version": "6.2.1", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "flutter_bloc", "version": "8.1.6", "dependencies": ["bloc", "flutter", "provider"]}, {"name": "flutter_svg", "version": "2.1.0", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "jwt_decoder", "version": "2.0.1", "dependencies": []}, {"name": "local_auth", "version": "2.3.0", "dependencies": ["flutter", "local_auth_android", "local_auth_darwin", "local_auth_platform_interface", "local_auth_windows"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "get_it", "version": "8.0.3", "dependencies": ["async", "collection", "meta"]}, {"name": "dio", "version": "5.8.0+1", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "path"]}, {"name": "sound_mode", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "root_jailbreak_sniffer", "version": "1.0.6", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_mute", "version": "0.0.4", "dependencies": ["flutter"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "watcher", "version": "1.1.1", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "dart_style", "version": "3.1.0", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "build_runner_core", "version": "8.0.0", "dependencies": ["async", "build", "build_config", "build_resolvers", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_resolvers", "version": "2.4.4", "dependencies": ["analyzer", "async", "build", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform", "yaml"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "build", "version": "2.4.2", "dependencies": ["analyzer", "async", "convert", "crypto", "glob", "logging", "meta", "package_config", "path"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "analyzer", "version": "7.4.5", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "lean_builder", "version": "0.1.0-alpha.10", "dependencies": ["_fe_analyzer_shared", "analyzer", "ansicolor", "args", "collection", "dart_style", "flutter_test", "frontend_server_client", "glob", "<PERSON><PERSON><PERSON><PERSON>", "lints", "meta", "path", "source_span", "stack_trace", "test", "watcher", "xxh3", "yaml"]}, {"name": "source_gen", "version": "2.0.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "pub_semver", "source_span", "yaml"]}, {"name": "firebase_remote_config_web", "version": "1.8.4", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_remote_config_platform_interface", "flutter", "flutter_web_plugins"]}, {"name": "firebase_remote_config_platform_interface", "version": "1.5.4", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_platform_interface", "version": "5.4.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "archive", "version": "3.6.1", "dependencies": ["crypto", "path"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "synchronized", "version": "3.3.1", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "audioplayers_windows", "version": "4.2.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_web", "version": "5.1.0", "dependencies": ["audioplayers_platform_interface", "flutter", "flutter_web_plugins", "web"]}, {"name": "audioplayers_platform_interface", "version": "7.1.0", "dependencies": ["collection", "flutter", "meta", "plugin_platform_interface"]}, {"name": "audioplayers_linux", "version": "4.2.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_darwin", "version": "6.2.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_android", "version": "5.2.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "pointer_interceptor", "version": "0.10.1+2", "dependencies": ["flutter", "flutter_web_plugins", "pointer_interceptor_ios", "pointer_interceptor_platform_interface", "pointer_interceptor_web"]}, {"name": "flutter_keyboard_visibility", "version": "6.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_platform_interface", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows", "meta"]}, {"name": "win32_registry", "version": "1.1.5", "dependencies": ["ffi", "win32"]}, {"name": "win32", "version": "5.13.0", "dependencies": ["ffi"]}, {"name": "device_info_plus_platform_interface", "version": "7.0.2", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "webview_flutter_wkwebview", "version": "3.22.0", "dependencies": ["flutter", "meta", "path", "webview_flutter_platform_interface"]}, {"name": "webview_flutter_platform_interface", "version": "2.13.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "webview_flutter_android", "version": "4.7.0", "dependencies": ["flutter", "meta", "webview_flutter_platform_interface"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.16", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "syncfusion_officecore", "version": "29.2.7", "dependencies": ["flutter", "syncfusion_flutter_core"]}, {"name": "jiffy", "version": "6.4.3", "dependencies": ["intl", "quiver"]}, {"name": "path_drawing", "version": "1.0.1", "dependencies": ["flutter", "meta", "path_parsing", "vector_math"]}, {"name": "universal_platform", "version": "1.1.0", "dependencies": []}, {"name": "html", "version": "0.15.6", "dependencies": ["csslib", "source_span"]}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "timezone", "version": "0.9.4", "dependencies": ["path"]}, {"name": "flutter_local_notifications_platform_interface", "version": "7.2.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_local_notifications_linux", "version": "4.0.1", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "firebase_analytics_web", "version": "0.5.10+12", "dependencies": ["_flutterfire_internals", "firebase_analytics_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins"]}, {"name": "firebase_analytics_platform_interface", "version": "4.3.6", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_web", "version": "2.23.0", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "flutter_localizations", "version": "0.0.0", "dependencies": ["characters", "clock", "collection", "flutter", "intl", "material_color_utilities", "meta", "path", "vector_math"]}, {"name": "easy_logger", "version": "0.0.2", "dependencies": ["flutter"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "hive", "version": "2.2.3", "dependencies": ["crypto", "meta"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "bloc", "version": "8.1.4", "dependencies": ["meta"]}, {"name": "vector_graphics_compiler", "version": "1.1.17", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "vector_graphics_codec", "version": "1.1.13", "dependencies": []}, {"name": "vector_graphics", "version": "1.1.18", "dependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "local_auth_windows", "version": "1.0.11", "dependencies": ["flutter", "local_auth_platform_interface"]}, {"name": "local_auth_platform_interface", "version": "1.0.10", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "local_auth_darwin", "version": "1.4.3", "dependencies": ["flutter", "intl", "local_auth_platform_interface"]}, {"name": "local_auth_android", "version": "1.0.49", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "intl", "local_auth_platform_interface"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "checked_yaml", "version": "2.0.3", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "built_value", "version": "8.10.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "_fe_analyzer_shared", "version": "82.0.0", "dependencies": ["meta"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "4.3.0", "dependencies": ["collection", "logging", "path", "stream_transform", "vm_service", "watcher"]}, {"name": "test", "version": "1.25.15", "dependencies": ["analyzer", "async", "boolean_selector", "collection", "coverage", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "path", "pool", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_span", "stack_trace", "stream_channel", "test_api", "test_core", "typed_data", "web_socket_channel", "webkit_inspection_protocol", "yaml"]}, {"name": "ansicolor", "version": "2.0.3", "dependencies": []}, {"name": "xxh3", "version": "1.2.0", "dependencies": []}, {"name": "_flutterfire_internals", "version": "1.3.55", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "pointer_interceptor_web", "version": "0.10.2+1", "dependencies": ["flutter", "flutter_web_plugins", "plugin_platform_interface", "pointer_interceptor_platform_interface", "web"]}, {"name": "pointer_interceptor_platform_interface", "version": "0.10.0+1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "pointer_interceptor_ios", "version": "0.10.1", "dependencies": ["flutter", "plugin_platform_interface", "pointer_interceptor_platform_interface"]}, {"name": "flutter_keyboard_visibility_windows", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_web", "version": "2.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface", "flutter_web_plugins"]}, {"name": "flutter_keyboard_visibility_macos", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_linux", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_platform_interface", "version": "2.0.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "quiver", "version": "3.2.2", "dependencies": ["matcher"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.28", "dependencies": ["flutter"]}, {"name": "webkit_inspection_protocol", "version": "1.2.1", "dependencies": ["logging"]}, {"name": "test_core", "version": "0.6.8", "dependencies": ["analyzer", "args", "async", "boolean_selector", "collection", "coverage", "frontend_server_client", "glob", "io", "meta", "package_config", "path", "pool", "source_map_stack_trace", "source_maps", "source_span", "stack_trace", "stream_channel", "test_api", "vm_service", "yaml"]}, {"name": "shelf_static", "version": "1.1.3", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "shelf_packages_handler", "version": "3.0.2", "dependencies": ["path", "shelf", "shelf_static"]}, {"name": "node_preamble", "version": "2.0.2", "dependencies": []}, {"name": "coverage", "version": "1.14.0", "dependencies": ["args", "cli_config", "glob", "logging", "meta", "package_config", "path", "pubspec_parse", "source_maps", "stack_trace", "vm_service"]}, {"name": "source_maps", "version": "0.10.13", "dependencies": ["source_span"]}, {"name": "source_map_stack_trace", "version": "2.1.2", "dependencies": ["path", "source_maps", "stack_trace"]}, {"name": "cli_config", "version": "0.2.0", "dependencies": ["args", "yaml"]}], "configVersion": 1}