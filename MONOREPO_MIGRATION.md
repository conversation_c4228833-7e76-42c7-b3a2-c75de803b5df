# Monorepo Migration Documentation

This document details the complete migration process from two separate repositories to a unified monorepo structure while preserving full commit history and version tags.

## Overview

**Migration Date**: December 2024  
**Source Repositories**:
- `scad-mobile` → `bayaan-private` (3,235 commits)
- `scad-mobile-public` → `bayaan-public` (368 commits)

**Final Result**: Unified monorepo with 3,607 total commits and 17 migrated version tags

## Pre-Migration State

### Repository Structure
```
bayaan-mobile/
├── scad-mobile/           # Private repository (separate git repo)
└── scad-mobile-public/    # Public repository (separate git repo)
```

### Repository Details
- **scad-mobile**: 3,235 commits on `next` branch
- **scad-mobile-public**: 368 commits on `next` branch
- **Version tags**: 17 total (10 private + 7 public)

## Migration Steps

### Step 1: Verify Latest Branches
```bash
# Ensure both repositories are on latest 'next' branch
cd scad-mobile
git checkout next
git pull origin next

cd ../scad-mobile-public  
git checkout next
git pull origin next
```

### Step 2: Initialize Monorepo
```bash
# Navigate to parent directory
cd ..

# Initialize new git repository
git init

# Create initial README
echo "# Bayaan Mobile Monorepo..." > README.md
git add README.md
git commit -m "Initial commit: Create Bayaan mobile monorepo"
```

### Step 3: Migrate Repositories with History Preservation
```bash
# Add scad-mobile as bayaan-private (preserving all 3,235 commits)
git subtree add --prefix=bayaan-private scad-mobile/ next

# Add scad-mobile-public as bayaan-public (preserving all 368 commits)  
git subtree add --prefix=bayaan-public scad-mobile-public/ next
```

**Result**: All commit history preserved with proper path prefixes

### Step 4: Migrate Version Tags
```bash
# Add original repositories as temporary remotes
git remote add private-origin ./scad-mobile
git remote add public-origin ./scad-mobile-public

# Fetch all tags from both repositories
git fetch private-origin --tags
git fetch public-origin --tags
```

#### Tag Migration Script
Created automated script to migrate tags with prefixes:

**Private Repository Tags** (10 tags):
- `v1.0.12` → `private/v1.0.12`
- `v1.0.13` → `private/v1.0.13`
- ... through `v1.0.20`

**Public Repository Tags** (7 tags):
- `v1.0.3` → `public/v1.0.3`
- `v1.0.4` → `public/v1.0.4`
- ... through `v1.0.9`

```bash
# For each tag, preserve annotated tag messages and metadata
for tag in v1.0.*; do
    commit_hash=$(git rev-list -n 1 "$tag")
    tag_message=$(git tag -l --format='%(contents)' "$tag")
    echo "$tag_message" | git tag -a "prefix/$tag" "$commit_hash" -F -
done
```

### Step 5: Cleanup
```bash
# Remove original duplicate tags
git tag -d v1.0.3 v1.0.4 ... v1.0.20

# Remove temporary remotes
git remote remove private-origin public-origin

# Remove original repository directories
rm -rf scad-mobile scad-mobile-public

# Add .gitignore for system files
echo ".DS_Store\n.augmentignore\n..." > .gitignore
git add .gitignore
git commit -m "Add .gitignore for monorepo system files"
```

## Final Structure

### Directory Layout
```
bayaan-mobile/
├── .gitignore                  # System files exclusion
├── README.md                   # Monorepo documentation
├── MONOREPO_MIGRATION.md       # This migration guide
├── bayaan-private/             # Former scad-mobile
│   ├── lib/
│   ├── android/
│   ├── ios/
│   ├── pubspec.yaml
│   └── ... (all original files)
└── bayaan-public/              # Former scad-mobile-public
    ├── lib/
    ├── android/
    ├── ios/
    ├── pubspec.yaml
    └── ... (all original files)
```

### Git History Summary
- **Total commits**: 3,607
  - 1 initial monorepo commit
  - 3,235 commits from scad-mobile (with `bayaan-private/` prefix)
  - 368 commits from scad-mobile-public (with `bayaan-public/` prefix)
  - 2 subtree merge commits
  - 1 cleanup commit

### Version Tags
- **17 version tags** migrated with full metadata preservation
- **Private tags**: `private/v1.0.12` through `private/v1.0.20`
- **Public tags**: `public/v1.0.3` through `public/v1.0.9`
- **All tag messages and tagger information preserved**

## Verification Commands

### Check Commit Count
```bash
git rev-list --count HEAD
# Expected: 3,607
```

### List Migrated Tags
```bash
git tag -l "private/*" "public/*" | sort -V
# Expected: 17 tags with proper prefixes
```

### Verify Tag Messages
```bash
git show private/v1.0.20 --no-patch
git show public/v1.0.9 --no-patch
# Should show original tag messages and metadata
```

### Check Directory Structure
```bash
ls -la
# Should show only: .git/, bayaan-private/, bayaan-public/, README.md, etc.
```

## Benefits Achieved

✅ **Complete History Preservation**: All 3,603 original commits maintained  
✅ **Full Tag Migration**: All version tags with messages preserved  
✅ **Clean Structure**: Organized monorepo with clear separation  
✅ **No Data Loss**: Every commit, tag, and metadata preserved  
✅ **Future-Ready**: Proper structure for continued development  

## Post-Migration Tasks

### Immediate
- [ ] Test both applications in new locations
- [ ] Verify Flutter dependencies resolve correctly
- [ ] Update local development workflows

### CI/CD Updates
- [ ] Update build scripts to reference new paths
- [ ] Modify deployment pipelines for `bayaan-private/` and `bayaan-public/`
- [ ] Update any automated testing paths

### Documentation Updates  
- [ ] Update project documentation with new structure
- [ ] Inform team members of new repository layout
- [ ] Update any external references to old repository names

## Troubleshooting

### If Applications Don't Build
1. Check Flutter dependencies in each `pubspec.yaml`
2. Run `flutter clean && flutter pub get` in each directory
3. Verify all asset paths are relative to new directory structure

### If Git History Seems Incomplete
1. Use `git log --follow <file>` to trace file history across the migration
2. Check `git log --all --graph` to see complete commit tree
3. Verify commit count matches expected total (3,607)

### If Tags Are Missing
1. Check `git tag -l` for all tags
2. Use `git show <tag>` to verify tag content
3. Confirm tag prefixes (`private/` and `public/`) are correct

---

**Migration completed successfully with zero data loss and full history preservation.**
