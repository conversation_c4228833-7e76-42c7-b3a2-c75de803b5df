// Copyright 2018 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

plugins {
    id 'com.android.library'
    id("kotlin-android")
}

def dartDefines = [:]
if (project.hasProperty('dart-defines')) {
    dartDefines = dartDefines + project.property('dart-defines')
            .split(',')
            .collectEntries { entry ->
                def decoded = new String(entry.decodeBase64(), 'UTF-8')
                def pair = decoded.split('=', 2)
                pair.length == 2 ? [(pair.first()): pair.last()] : [:]
            }
}

android {
    namespace "com.google.firebase.installations"
    compileSdkVersion project.compileSdkVersion
    defaultConfig {
        minSdkVersion project.minSdkVersion
        targetSdkVersion project.targetSdkVersion
        multiDexEnabled true
        versionName version
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        buildConfigField("String", "VERSION_NAME", "\"${version}\"")
        resValue("string", "encrypt_secret", dartDefines.encryptSecret.toString())
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions { jvmTarget = "1.8" }
    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }
    buildFeatures {
        buildConfig true
    }
}

dependencies {
    api 'com.google.android.gms:play-services-tasks:18.0.1'
    api 'com.google.firebase:firebase-annotations:16.2.0'
    api("com.google.firebase:firebase-common:21.0.0")
    api("com.google.firebase:firebase-common-ktx:21.0.0")
    api("com.google.firebase:firebase-components:18.0.0")
    api project(':firebase-installations-interop')

    implementation libs.kotlin.stdlib

    compileOnly "com.google.auto.value:auto-value-annotations:1.6.5"

    annotationProcessor "com.google.auto.value:auto-value:1.6.2"

    testImplementation libs.androidx.test.core
    testImplementation libs.truth
    testImplementation 'junit:junit:4.12'
    testImplementation 'junit:junit:4.13'
    testImplementation libs.mockito.core
    testImplementation 'org.mockito:mockito-inline:5.2.0'
    testImplementation libs.robolectric

    androidTestImplementation "androidx.annotation:annotation:1.0.0"
    androidTestImplementation 'androidx.test:runner:1.2.0'
    androidTestImplementation libs.androidx.test.junit
    androidTestImplementation libs.truth
    androidTestImplementation 'junit:junit:4.12'
    androidTestImplementation 'org.mockito:mockito-core:2.25.0'
    androidTestImplementation 'org.mockito:mockito-inline:2.25.0'
}
