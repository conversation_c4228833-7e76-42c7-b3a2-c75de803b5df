<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <!--Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds-->
    <!--<uses-sdk android:minSdkVersion="21"/>-->
    <application>
        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:exported="false"> <meta-data android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar" android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>
    </application>
</manifest>
